package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type DigispectHandler struct {
	DigispectUseCase *usecase.DigispectUseCase
}

func NewDigispectHandler(
	digispectUseCase *usecase.DigispectUseCase,
) *DigispectHandler {
	return &DigispectHandler{
		DigispectUseCase: digispectUseCase,
	}
}

// brand
func (h *DigispectHandler) GetDigispectBrandConfigList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.DigispectBrandConfigListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.DigispectUseCase.GetDigispectBrandsConfigList(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) CreateDigispectBrand(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.CreateDigispectBrandReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.DigispectUseCase.CreateDigispectBrandsConfig(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) UpdateDigispectBrandsConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.UpdateDigispectBrandReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	id := c.Param("id")
	req.BrandID = id

	resp, err := h.DigispectUseCase.UpdateDigispectBrandsConfig(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// digispect config
func (h *DigispectHandler) GetDigispectConfigList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.DigispectConfigListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.DigispectUseCase.GetDigispectConfigList(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) CreateDigispectConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.CreateDigispectConfigReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.DigispectUseCase.CreateDigispectConfig(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) UpdateDigispectConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.CreateDigispectConfigReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	req.DigispectConfigID = c.Param("id")

	resp, err := h.DigispectUseCase.UpdateDigispectConfig(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) CreateDigispectVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.CreateDigispectVehicleReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.DigispectUseCase.CreateDigispectVehicle(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) UpdateDigispectVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	req := dtos.UpdateDigispectVehicleReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.DigispectUseCase.UpdateDigispectVehicle(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) UpdateDigispectVehicleAxleConfiguration(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	req := dtos.UpdateDigispectVehicleAxleConfigurationReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.DigispectUseCase.UpdateDigispectVehicleAxleConfiguration(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) DeleteDigispectVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.DigispectUseCase.DeleteDigispectVehicle(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) GetDigispectVehicleList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.DigispectVehicleListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.DigispectUseCase.GetDigispectVehicleList(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) GetDigispectVehicleCustomers(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.DigispectVehicleCustomerListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.DigispectUseCase.GetDigispectVehicleCustomers(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) GetDigispectPackages(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.DigispectUseCase.GetDigispectPackages(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *DigispectHandler) ChartTop5DigispectVehicleBrands(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.DigispectUseCase.ChartTop5DigispectVehicleBrands(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
