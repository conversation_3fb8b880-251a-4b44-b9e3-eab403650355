package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
)

type DigispectRepository interface {
	UpsertDigispectBrand(ctx context.Context, dB database.DBI, brand *models.DigispectBrandConfig) error
	GetDigispectBrandList(ctx context.Context, dB database.DBI, param models.DigispectBrandConfigParam) (int, []models.DigispectBrandConfig, error)
	GetDigispectBrand(ctx context.Context, dB database.DBI, param models.DigispectBrandConfigParam) (*models.DigispectBrandConfig, error)

	UpsertDigispectConfig(ctx context.Context, dB database.DBI, digiConf *models.DigispectConfig) error
	UpdateDigispectConfig(ctx context.Context, dB database.DBI, digiConf *models.DigispectConfig, param models.DigispectConfigParam) error
	GetDigispectConfigList(ctx context.Context, dB database.DBI, param models.DigispectConfigParam) (int, []models.DigispectConfig, error)
	GetDigispectConfig(ctx context.Context, dB database.DBI, param models.DigispectConfigParam) (*models.DigispectConfig, error)

	CreateDigispectVehicle(ctx context.Context, dB database.DBI, vehicle *models.DigispectVehicle) error
	UpdateDigispectVehicle(ctx context.Context, dB database.DBI, id string, vehicle *models.DigispectVehicle) error
	GetDigispectVehicleList(ctx context.Context, dB database.DBI, param models.GetDigispectVehicleListParam) (int, []models.DigispectVehicleInpected, error)
	GetDigispectVehicle(ctx context.Context, dB database.DBI, cond models.DigispectVehicleCondition) (*models.DigispectVehicle, error)
	GetDigispectVehicles(ctx context.Context, dB database.DBI, cond models.DigispectVehicleCondition) ([]models.DigispectVehicle, error)
	DeleteDigispectVehicle(ctx context.Context, dB database.DBI, id string) error
	GetDigispectVehicleCustomers(ctx context.Context, dB database.DBI, param models.GetDigispectVehicleCustomerListParam) (int, []string, error)
	ChartTop5DigispectVehicleBrands(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
}
