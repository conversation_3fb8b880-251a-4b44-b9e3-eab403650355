package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"

	"github.com/lib/pq"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type digispectRepository struct{}

func NewDigispectRepository() repository.DigispectRepository {
	return &digispectRepository{}
}

func enrichDigispectBrandConfigWithWhere(db *gorm.DB, where models.DigispectBrandConfigWhere) {
	if where.ID != "" {
		db.Where("id = ?", where.ID)
	}

	if where.ClientID != "" {
		db.Where("client_id = ?", where.ClientID)
	}

	if len(where.TypeCodes) > 0 {
		db.Where("type_code && ?", pq.StringArray(where.TypeCodes))
	}

	if where.StatusCode != "" {
		db.Where("status_code = ?", where.StatusCode)
	}

	if where.LowerName != "" {
		db.Where("lower(brand_name) = ?", where.LowerName)
	}
}

func (d *digispectRepository) UpsertDigispectBrand(ctx context.Context, dB database.DBI, brand *models.DigispectBrandConfig) error {
	q := dB.GetTx().Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"updated_at",
			"brand_name",
			"status_code",
			"type_code",
		}),
	})

	if brand.ID == "" {
		return q.Create(brand).Error
	}

	return q.Session(&gorm.Session{SkipHooks: true}).Updates(brand).Error
}

func (d *digispectRepository) GetDigispectBrandList(ctx context.Context, dB database.DBI, param models.DigispectBrandConfigParam) (int, []models.DigispectBrandConfig, error) {
	var totalRecords int64
	dgBrand := []models.DigispectBrandConfig{}
	query := dB.GetTx().Model(&dgBrand)
	enrichDigispectBrandConfigWithWhere(query, param.Cond.Where)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().Where("LOWER(brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&dgBrand).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), dgBrand, nil
}

func (d *digispectRepository) GetDigispectBrand(ctx context.Context, dB database.DBI, param models.DigispectBrandConfigParam) (*models.DigispectBrandConfig, error) {
	dgBrand := &models.DigispectBrandConfig{}
	query := dB.GetOrm().Model(dgBrand)
	enrichDigispectBrandConfigWithWhere(query, param.Cond.Where)

	err := query.First(dgBrand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("digispect brand")
		}

		return nil, err
	}

	return dgBrand, err
}

func (d *digispectRepository) UpsertDigispectConfig(ctx context.Context, dB database.DBI, digiConf *models.DigispectConfig) error {
	q := dB.GetTx().Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"updated_at",
			"brand_digispect_id",
			"status_code",
			"type_code",
			"model",
			"size",
		}),
	})

	// CREATE
	if digiConf.ID == "" {
		return q.Create(digiConf).Error
	}

	// UPDATE
	digiConf.BrandDigispectID = ""
	return q.Session(&gorm.Session{SkipHooks: true}).Updates(digiConf).Error
}

func (d *digispectRepository) UpdateDigispectConfig(ctx context.Context, dB database.DBI, digiConf *models.DigispectConfig, param models.DigispectConfigParam) error {
	query := dB.GetTx().Model(digiConf)
	enrichDigispectConfigWithWhere(query, param.Cond.Where)

	return query.Updates(digiConf).Error
}

func (d *digispectRepository) GetDigispectConfigList(ctx context.Context, dB database.DBI, param models.DigispectConfigParam) (int, []models.DigispectConfig, error) {
	var totalRecords int64
	dgconf := []models.DigispectConfig{}
	query := dB.GetTx().Model(&dgconf)
	enrichDigispectConfigWithWhere(query, param.Cond.Where)
	enrichDigispectConfigWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(model) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(size) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&dgconf).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), dgconf, nil

}

func (d *digispectRepository) GetDigispectConfig(ctx context.Context, dB database.DBI, param models.DigispectConfigParam) (*models.DigispectConfig, error) {
	dgConfig := &models.DigispectConfig{}
	query := dB.GetOrm().Model(dgConfig)
	enrichDigispectConfigWithWhere(query, param.Cond.Where)

	err := query.First(dgConfig).Error

	return dgConfig, err
}

func enrichDigispectConfigWithWhere(db *gorm.DB, where models.DigispectConfigWhere) {

	if where.ClientID != "" {
		db.Where("client_id = ?", where.ClientID)
	}
	if where.ID != "" {
		db.Where("id = ?", where.ID)
	}
	if where.BrandDigiscpectID != "" {
		db.Where("brand_digispect_id = ?", where.BrandDigiscpectID)
	}
	if len(where.BrandDigiscpectIDs) > 0 {
		db.Where("brand_digispect_id IN ?", where.BrandDigiscpectIDs)
	}

	if len(where.TypeCodes) > 0 {
		db.Where("type_code IN ?", where.TypeCodes)
	}
}

func enrichDigispectConfigWithPreload(db *gorm.DB, preload models.DigispectConfigPreload) {
	if preload.BrandDigiscpect {
		db.Preload("BrandDigiscpect")
	}

	if preload.DigispectType {
		db.Preload("DigispectType")
	}
}

func enrichDigispectVehicleWithPreload(db *gorm.DB, preload models.DigispectVehiclePreload) {
	if preload.InspectionVechicles {
		db.Preload("InspectionVechicles")
	}

	if preload.InspectionVechiclesVehicle {
		db.Preload("InspectionVechicles.AssetVehicle")
	}
}

func (r *digispectRepository) CreateDigispectVehicle(ctx context.Context, dB database.DBI, vehicle *models.DigispectVehicle) error {
	return dB.GetTx().Create(vehicle).Error
}

func (r *digispectRepository) UpdateDigispectVehicle(ctx context.Context, dB database.DBI, id string, vehicle *models.DigispectVehicle) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(vehicle).
		Error
}

func (r *digispectRepository) GetDigispectVehicle(ctx context.Context, dB database.DBI, cond models.DigispectVehicleCondition) (*models.DigispectVehicle, error) {
	vehicles := &models.DigispectVehicle{}
	query := dB.GetTx().Model(&vehicles)
	enrichDigispectVehicleWithWhere(query, cond.Where)
	err := query.First(&vehicles).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("DIGISPECT_VEHICLE")
		}
		return nil, err
	}

	return vehicles, nil
}

func (r *digispectRepository) GetDigispectVehicles(ctx context.Context, dB database.DBI, cond models.DigispectVehicleCondition) ([]models.DigispectVehicle, error) {
	vehicles := []models.DigispectVehicle{}
	query := dB.GetTx().Model(&models.DigispectVehicle{})
	enrichDigispectVehicleWithWhere(query, cond.Where)
	err := query.Find(&vehicles).Error
	if err != nil {
		return nil, err
	}

	return vehicles, nil
}

func (r *digispectRepository) GetDigispectVehicleList(ctx context.Context, dB database.DBI, param models.GetDigispectVehicleListParam) (int, []models.DigispectVehicleInpected, error) {
	var totalRecords int64
	vehicles := []models.DigispectVehicleInpected{}
	query := dB.GetTx().Model(&vehicles).Joins(`
		LEFT JOIN (
			SELECT 
				digispect_vehicle_id, 
				MAX(updated_at) AS last_inspected_at 
			FROM ams_asset_inspection_vehicle 
			GROUP BY digispect_vehicle_id
		) AS latest_inspections 
		ON latest_inspections.digispect_vehicle_id = ams_digispect_vehicles.id
	`).Select("ams_digispect_vehicles.*, latest_inspections.last_inspected_at")
	enrichDigispectVehicleWithWhere(query, param.Cond.Where)
	enrichDigispectVehicleWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().Where("LOWER(reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(partner_owner_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&vehicles).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), vehicles, nil
}

func enrichDigispectVehicleWithWhere(db *gorm.DB, where models.DigispectVehicleWhere) {
	if where.ID != "" {
		db.Where("id = ?", where.ID)
	}

	if len(where.IDs) > 0 {
		db.Where("id IN (?)", where.IDs)
	}

	if where.ClientID != "" {
		db.Where("client_id = ?", where.ClientID)
	}

	if where.WithOrmDeleted {
		db.Unscoped()
	}

	if where.ReferenceNumber != "" {
		db.Where("reference_number = ?", where.ReferenceNumber)
	}

	if where.LowerReferenceNumber != "" {
		db.Where("LOWER(reference_number) = LOWER(?)", where.LowerReferenceNumber)
	}
}

func (r *digispectRepository) DeleteDigispectVehicle(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().
		Where("id = ?", id).
		Delete(&models.DigispectVehicle{}).
		Error
}

func (d *digispectRepository) GetDigispectVehicleCustomers(ctx context.Context, dB database.DBI, param models.GetDigispectVehicleCustomerListParam) (int, []string, error) {
	var totalRecords int64
	var customers []string

	query := dB.GetTx().Table("ams_digispect_vehicles").
		Select("DISTINCT partner_owner_name").
		Where("client_id = ? AND deleted_at IS NULL AND partner_owner_name IS NOT NULL AND partner_owner_name != ''", param.ClientID)

	if param.SearchKeyword != "" {
		query.Where(
			"LOWER(partner_owner_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%",
		)
	}

	// Count total distinct values
	countQuery := dB.GetTx().Table("ams_digispect_vehicles").
		Select("COUNT(DISTINCT partner_owner_name)").
		Where("client_id = ? AND deleted_at IS NULL AND partner_owner_name IS NOT NULL AND partner_owner_name != ''", param.ClientID)

	if param.SearchKeyword != "" {
		countQuery.Where(
			"LOWER(partner_owner_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%",
		)
	}

	err := countQuery.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	// Get paginated results
	query.Order("partner_owner_name ASC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&customers).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), customers, nil
}

func (d *digispectRepository) ChartTop5DigispectVehicleBrands(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.DigispectVehicle{})

	// Apply client filter
	query.Where("client_id = ?", req.ClientID)

	// Apply date filters if provided
	if !req.StartDatetime.IsZero() {
		query.Where("created_at >= ?", req.StartDatetime)
	}
	if !req.EndDatetime.IsZero() {
		query.Where("created_at <= ?", req.EndDatetime)
	}

	// Filter out empty brand names
	query.Where("digispect_brand_name IS NOT NULL AND digispect_brand_name != ''")

	// Group by brand name, count occurrences, and get top 5
	query.Select(
		"digispect_brand_name AS name",
		"COUNT(*) AS y",
	)
	query.Group("digispect_brand_name")
	query.Order("y DESC")
	query.Limit(5)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}
